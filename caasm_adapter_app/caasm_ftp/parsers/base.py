import io

from caasm_tool.util import restore, extract


class BaseParser(object):
    _EMPTY_VALUES = ([], {}, (), "", None)
    _ASSET_TYPE_FIELD = "base.asset_type"
    _MODEL_NAME_FIELD = "base.model_name"

    def __init__(self, ftp_client, *args, **kwargs):
        self._ftp_client = ftp_client
        self._file_name = kwargs.get("share_name", None)

    def handle(self):
        contents = []
        self._ftp_client.cwd(self.get_file_dir())
        if self._file_name:
            all_files = [self._file_name]
        else:
            all_files = self.file_names
        for file_name in all_files:
            writer = io.BytesIO()
            self._ftp_client.retrbinary(f"retr {file_name}", writer.write)
            contents.append(writer)

        records = []
        for content in contents:
            _d_content = content.getvalue().decode("utf-8")
            records.extend(self.clean(_d_content.splitlines(keepends=True)))

        result = []
        for info in records:
            self.enrich(info)
            result.append(info)
        return result

    def enrich(self, info):
        pass

    def clean(self, content):
        raise NotImplementedError

    @classmethod
    def get_name(cls):
        raise NotImplementedError

    @property
    def file_names(self):
        raise NotImplementedError

    @classmethod
    def get_file_dir(cls):
        return NotImplementedError

    @staticmethod
    def get_category():
        raise NotImplementedError

    @property
    def ftp_client(self):
        return self._ftp_client

    @classmethod
    def restore(cls, field, value, record):
        restore(field, value, record) if value not in cls._EMPTY_VALUES else ...

    @classmethod
    def extract(cls, record, field):
        return extract(record, field)
