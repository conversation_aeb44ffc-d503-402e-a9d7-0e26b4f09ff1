name: caasm_ftp
display_name: "FTP适配器"
description: "通过FTP方式下载数据文件，再解析文件获取到数据"
type: "文件导入"
company: "北京未岚科技有限公司"
version: "0.1"
logo: "caasm_ftp.png"
priority: 0
properties:

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)?(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: username
    type: string
    required: false
    display_name: "用户名"
    description: "用户名称"
    validate_rules:
      - name: length
        error_hint: "用户名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: password
    type: password
    required: false
    display_name: "密码"
    description: "密码信息"
    validate_rules:
      - name: length
        error_hint: "密码长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: share_name
    type: string
    required: false
    display_name: "文件夹名称"
    description: "文件夹"
    validate_rules:
      - name: length
        error_hint: "文件名长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: priority
    type: integer
    required: true
    display_name: "优先值"
    description: "优先值"
    validate_rules:
      - name: number
        error_hint: "优先值信息无效。最小值不得小于1，最大值不得大于1000"
        setting:
          min: 1
          max: 1000

  - name: parser
    type: choice
    required: true
    display_name: "解析器"
    description: "文件解析器，用于解析文件内容"
    validate_rules:
      - name: single_choice
        error_hint: "文件解析器无效，请选择正确的文件解析"
        setting:
          choice: [ ]
          dynamic: true
          point_define: "caasm_ftp.util:find_parse_names"
          point_to: choice

fetch_setting:
  type: disposable
  point: "caasm_ftp.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "caasm_ftp.fetch:get_authentication"
  test_connection_point: "caasm_ftp.fetch:get_connection"
  size: 100
  fetch_type_mapper: { }

merge_setting:
  size: 100

convert_setting:
  size: 100
