from threatbook_tdp.clients.api import ThreatBookTDPAPIDomainListClient, ThreatBookTDPAPIListClient
from threatbook_tdp.clients.asset import ThreatBookTDPAssetMachineListClient
from threatbook_tdp.clients.auth import ThreatBook<PERSON><PERSON><PERSON>uth<PERSON>lient, ThreatBookTDPCaptchaClient
from threatbook_tdp.clients.domain import ThreatBookTDPDomainListClient


class ClientType(object):
    CAPTCHA = "captcha"
    ASSET_LIST = "computer"
    DOMAIN_LIST = "domain"
    AUTH = "auth"
    API_HOST = "api_host"
    API = "api"


class ThreatBookTDPManager(object):
    _client_mapper = {
        ClientType.CAPTCHA: ThreatBookTDPCaptchaClient,
        ClientType.AUTH: ThreatBookTDPAuthClient,
        ClientType.ASSET_LIST: ThreatBookTDPAssetMachineListClient,
        ClientType.DOMAIN_LIST: ThreatBookTDPDomainListClient,
        ClientType.API_HOST: ThreatBookTDPAPIDomainListClient,
        ClientType.API: ThreatBookTDPAPIListClient,
    }

    def __init__(self, connection, session=None, condition=None):
        self._connection = connection
        self._session = session
        self._condition = condition
        self._client_instance_mapper = {}
        self.auth_status = False
        self._find_method_mapper = {ClientType.API: self.find_api}

    def find_api(self, fetch_type=None, page_index=None, page_size=None):
        domain_names = self._condition.get("domain_names")
        if not domain_names:
            return []
        host = domain_names.pop()
        if not host or not host.get("host"):
            return []
        result = []
        domain = host.get("host")
        _index = 1
        while True:
            try:
                data = self._call(ClientType.API, host_name=domain, page_index=_index, page_size=200)
                if not data:
                    break
                _index += 1
                result.extend(data)
            except Exception as e:
                break
        return result

    def find_default(self, fetch_type=None, page_index=None, page_size=None):
        return self._call(fetch_type, page_index=page_index, page_size=page_size)

    def find_asset(self, fetch_type, page_index=1, page_size=20):
        self.auth() if not self.auth_status else ...
        return self._find_method_mapper.get(fetch_type, self.find_default)(
            fetch_type=fetch_type, page_index=page_index, page_size=page_size
        )

    def auth(self):
        self._call(ClientType.ASSET_LIST, page_index=1, page_size=20)
        self.auth_status = True

    def _call(self, client_type, *args, **kwargs):
        if client_type not in self._client_instance_mapper:
            instance = self._client_mapper[client_type](self._connection, self._session)
            self._client_instance_mapper[client_type] = instance
        return self._client_instance_mapper[client_type].handle(*args, **kwargs)
