name: "threatbook_tdp"
display_name: "微步在线威胁感知平台"
description: "微步在线威胁感知平台TDP是基于流量分析与情报驱动的网络威胁检测与响应平台，全面覆盖传统僵木蠕、APT、web和非web攻击、业务风险挖掘、资产梳理。 TDP围绕用户实际安全运营场景，为用户提供一款告警准确、好用有效且持续创新的产品，从而降低用户安全运营成本，提升安全运营效果。 TDP聚焦真实威胁。实现安全治理闭环： - 帮助企业梳理和预见风险 - 提升企业检测和响应效率 - 降低企业安全运营成本"
type: "流量检测"
company: "微步在线"
logo: "logo.jpeg"
version: "0.8"
priority: 1
properties:
  - 威胁检测
  - 流量检测

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: apikey
    type: string
    required: true
    display_name: "apikey"
    description: "apikey"
    validate_rules:
      - name: length
        error_hint: "apikey长度必须大于等于1且小于等于1000"
        setting:
          min: 1
          max: 1000
  
  - name: secret_key
    type: string
    required: true
    display_name: "secret_key"
    description: "secret_key"
    validate_rules:
      - name: length
        error_hint: "secret_key长度必须大于等于1且小于等于1000"
        setting:
          min: 1
          max: 1000

  - name: is_active
    type: choice
    required: false
    default: "否"
    display_name: "活跃服务标识"
    description: "是否获取活跃服务的标识"
    validate_rules:
      - name: single_choice
        error_hint: "活跃服务标识无效"
        setting:
          choice:
            - 是
            - 否
          dynamic: false



fetch_setting:
  type: disposable
  point: "threatbook_tdp.fetch:find_asset"
  condition_point: "threatbook_tdp.fetch:build_query_condition"
  is_need_test_service: true
  test_auth_point: "threatbook_tdp.fetch:get_auth_connection"
  size: 100
  fetch_type_mapper:
    asset:
      - computer
      - api
  cleaner_mapper:
    asset:
      computer:
        - "threatbook_tdp.cleaners.computer_cleaner:ThreatBookTDPCleaner"

merge_setting:
  size: 300
  setting: { }

convert_setting:
  size: 300
  before_executor_mapper: { }
  executor_mapper: { }

fabric_setting:
  choose_point_mapper:
    asset: "threatbook_tdp.fabric:choose_new_record"