import time
import base64
import hmac
import hashlib

from caasm_adapter_base.util.client import FetchJsonResultClient
from caasm_adapter_base.exceptions.fetcher import AdapterFetchApiParamsInvalidException


class ThreatBookTDPClient(FetchJsonResultClient):
    referer_url = "/login?callback=/investigation/logquery&params={}"

    def build_request_params(self, *args, **kwargs):
        params = {}
        apikey = self._connection.get("apikey", None)
        secret_key = self._connection.get("secret_key", None)
        if not apikey or not secret_key:
            raise AdapterFetchApiParamsInvalidException()
        timestamp = int(time.mktime(time.localtime()))
        sign_data = f"{apikey}{timestamp}"
        sign = hmac.new(secret_key.encode("utf-8"), sign_data.encode("utf-8"), hashlib.sha256).digest()
        params["auth_timestamp"] = timestamp
        params["api_key"] = apikey
        params["sign"] = base64.urlsafe_b64encode(sign).decode("utf-8")
        return params

    @property
    def data_key_name(self):
        return "data"

    @property
    def flag_key_name(self):
        return "response_code"

    @property
    def success_flag(self):
        return 0

    def build_request_header(self, *args, **kwargs):
        referer = self.build_url(url=self.referer_url)
        headers = {"referer": referer}
        return headers


class ThreatBookTDPPostClient(ThreatBookTDPClient):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.items"
