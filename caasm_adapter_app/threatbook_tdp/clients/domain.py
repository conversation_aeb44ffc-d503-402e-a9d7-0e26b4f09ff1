from threatbook_tdp.clients.base import ThreatBookTDPPostClient


class ThreatBookTDPDomainListClient(ThreatBookTDPPostClient):
    URL = "/api/v1/assets/domainName/search"

    def build_request_json(self, page_index, page_size):
        json_data = {
            "condition": {
                "created_in_3_days": "",
                "has_privacy": "",
                "has_upload_api": "",
                "is_public": "",
                "time_range": "thirty_days",
                "is_active": "",
                "second_level_domain": "",
            },
            "page": {"cur_page": page_index, "page_size": page_size},
        }

        return json_data
