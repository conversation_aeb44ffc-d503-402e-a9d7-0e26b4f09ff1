from threatbook_tdp.clients.base import ThreatBookTDPPostClient


class ThreatBookTDPAssetMachineListClient(ThreatBookTDPPostClient):
    URL = "/api/v1/host/getFallHostSumList"

    def build_request_json(self, *args, **kwargs):
        is_active = self.connection.get("is_active") == "是"
        json_data = {
            "condition": {"assets_group": [], "service": "", "service_class": "", "is_active": is_active},
            "page": {"cur_page": kwargs.get("page_index", None), "page_size": kwargs.get("page_size", None)},
        }
        return json_data

    