from caasm_adapter_base.util.client import FetchJsonResultClient


class ThreatBookNGTIPClient(FetchJsonResultClient):

    @property
    def data_key_name(self):
        return "data.vulns"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def success_flag(self):
        return 200


class ThreatBookNGTIPPostClient(ThreatBookNGTIPClient):
    METHOD = "post"

    @property
    def data_key_name(self):
        return "data.vulns"

    @property
    def apikey(self):
        return self.connection.get("apikey", "")

    def build_request_params(self, *args, **kwargs):
        request_data = {
            "apikey": self.apikey,
        }
        return request_data
