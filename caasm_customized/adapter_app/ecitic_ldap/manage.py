from ecitic_ldap.clients.department import EciticLDAPDepartmentClient
from ecitic_ldap.clients.owner import EciticLDAPOwnerClient


class ClientType(object):
    OWNER = "owner"
    DEPARTMENT = "department"


_CLASS_MAPPER = {
    ClientType.OWNER: EciticLDAPOwnerClient,
    ClientType.DEPARTMENT: EciticLDAPDepartmentClient,
}


class EciticLDAPManager(object):
    def __init__(self, connection, session=None):
        self._connection = connection
        self._session = session

    def find(self, client_type, page_index, page_size):
        return self._call(client_type, page_index=page_index, page_size=page_size)

    def auth(self):
        self._call(ClientType.OWNER, page_index=1, page_size=1)

    def create_session(self):
        return self.__instance(ClientType.OWNER).client

    def _call(self, client_type, *args, **kwargs):
        return _CLASS_MAPPER[client_type](self._connection, self._session).handle(*args, **kwargs)

    def __instance(self, client_type):
        return _CLASS_MAPPER[client_type](self._connection, self._session)
