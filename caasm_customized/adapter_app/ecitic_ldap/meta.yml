name: "ecitic_ldap"
display_name: "中信证券LDAP"
description: "中信证券统一身份认证系统"
type: "账户"
company: "中信证券"
logo: "logo.png"
version: "0.1"
builtin: false
priority: 1
properties:
  - "LDAP"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'


  - name: app_key
    type: string
    required: true
    display_name: "应用标识"
    description: "应用标识"
    validate_rules:
      - name: length
        error_hint: "应用标识长度必须大于等于2且小于等于100"
        setting:
          min: 2
          max: 100

  - name: invoke_key
    type: password
    required: true
    display_name: "调用key"
    description: "调用key"
    validate_rules:
      - name: length
        error_hint: "调用key长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100

  - name: update_time
    type: string
    required: false
    display_name: "更新时间"
    description: "更新时间（yyyy-MM-dd HH:mm:ss.SSS），用于确定查询范围，更新时间到请求时间的范围信息"
    validate_rules:
      - name: length
        error_hint: "更新时间长度必须大于等于6且小于等于100"
        setting:
          min: 6
          max: 100


fetch_setting:
  type: disposable
  point: "ecitic_ldap.fetch:find_asset"
  is_need_test_service: true
  test_auth_point: "ecitic_ldap.fetch:get_auth_connection"
  size: 200
  fetch_type_mapper:
    owner:
      - owner
      - department
    department:
      - department
  cleaner_mapper:
    department:
      department:
        - "ecitic_ldap.cleaners.department_cleaner:DepartmentCleaner"
    owner:
      owner:
        - "ecitic_ldap.cleaners.owner_cleaner:OwnerCleaner"

fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 200
  setting: { }

convert_setting:
  size: 200
  before_executor_mapper: { }
  executor_mapper: { }