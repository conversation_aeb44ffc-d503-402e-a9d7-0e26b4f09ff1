from caasm_adapter.util.adapter_fetch import fetch_util

from ecitic_ldap.manage import EciticLDAPManager


def get_auth_connection(connection, session=None):
    return _manager(connection).auth()


def find_asset(connection, fetch_type, page_index=0, page_size=20, **kwargs):
    manager = _manager(connection, kwargs.get("condition").get("client"))
    records = manager.find(fetch_type, page_size=page_size, page_index=page_index + 1)
    result = fetch_util.build_asset(records, fetch_type)
    return fetch_util.return_success(result)


def build_query_condition(connection, *args, **kwargs):
    return {"client": _manager(connection).create_session()}


def _manager(connection, session=None):
    return EciticLDAPManager(connection, session)
