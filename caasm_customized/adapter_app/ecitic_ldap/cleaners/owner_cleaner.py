from ecitic_ldap.cleaners.department_cleaner import DepartmentCleaner


class OwnerCleaner(DepartmentCleaner):
    _DEFAULT_DATA_TYPE = "owner"

    def clean_internal(self, record, new_record):
        record["fetch_type"] = "owner"
        return record

    def build_common(self, biz_records):
        owners, departments = self._group_biz(biz_records)
        department_mapper = {i["orgCode"]: i for i in self._execute(departments)}

        result = []
        for owner in owners:
            owner["departmentName"] = department_mapper.get(owner["userDepart"], {}).get("fullName")
            result.append(owner)
        return result

    @classmethod
    def _group_biz(cls, biz_records):
        owners, departments = [], []
        for biz_record in biz_records:
            if "orgCode" in biz_record:
                departments.append(biz_record)
            else:
                owners.append(biz_record)
        return owners, departments
