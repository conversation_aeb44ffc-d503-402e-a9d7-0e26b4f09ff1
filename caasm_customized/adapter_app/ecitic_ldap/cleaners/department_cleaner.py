from caasm_adapter_base.fetcher.cleaners.base import FetchTotalBaseCleaner


class DepartmentCleaner(FetchTotalBaseCleaner):
    def build_common(self, biz_records):
        return self._execute(biz_records)

    @classmethod
    def _execute(cls, biz_records):
        department_mapper_by_id = {biz_record["orgId"]: biz_record for biz_record in biz_records}

        cls._padding_full_name(department_mapper_by_id)
        return biz_records

    @classmethod
    def _padding_full_name(cls, mapper):
        for org_id, org in mapper.items():
            org["fullName"] = cls._parse_full_name(org, mapper)

        for org_id, org in mapper.items():
            org["parentFullName"] = mapper.get(org["orgParentId"], {}).get("fullName", "")

    @classmethod
    def _parse_full_name(cls, org, org_mapper, title=""):
        if not title:
            title = org["orgName"]

        parent_id = org["orgParentId"]
        parent_org = org_mapper.get(parent_id)

        if not parent_org or parent_org == org:
            return title

        else:
            title = parent_org["orgName"] + "-" + title

        return cls._parse_full_name(parent_org, org_mapper, title=title)
