import datetime
import logging

from suds import <PERSON>lient

from caasm_tool.constants import DATETIME_MIC_FORMAT

log = logging.getLogger()


class EciticLDAPBaseClient(object):
    TIMEOUT = 1200

    def __init__(self, connection, client=None):
        self.connection = connection
        if not client:
            client = Client(self.address)
            client.set_options(timeout=self.TIMEOUT)
        self.client = client

    def handle(self, *args, **kwargs):
        request_args = self.get_action_args(*args, **kwargs)
        request_kwargs = self.get_action_kwargs(*args, **kwargs)
        response = self.execute(request_args, request_kwargs)
        return self.clean_response(response)

    def get_action_args(self, *args, **kwargs):
        return ()

    def get_action_kwargs(self, *args, **kwargs):
        return {}

    def execute(self, request_args, request_kwargs):
        method = getattr(self.client.service, self.method_name, None)
        if not method:
            log.warning(f"not found method({self.method_name}) implement")
            return
        return method(*request_args, **request_kwargs)

    def clean_response(self, response):
        return response

    @property
    def query_time(self):
        return datetime.datetime.now().strftime(DATETIME_MIC_FORMAT)[:-3]

    @property
    def update_time(self):
        return self.connection.get("update_time", "1995-10-25 00:00:00.000")

    @property
    def address(self):
        return self.connection.get("address")

    @property
    def app_key(self):
        return self.connection.get("app_key")

    @property
    def invoke_key(self):
        return self.connection.get("invoke_key")

    @property
    def method_name(self):
        raise NotImplementedError
