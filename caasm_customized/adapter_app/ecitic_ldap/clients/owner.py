import logging

from ecitic_ldap.clients.base import EciticLDAPBaseClient

log = logging.getLogger()


class EciticLDAPOwnerClient(EciticLDAPBaseClient):
    @property
    def method_name(self):
        return "getUsers"

    def get_action_args(self, page_index, page_size):
        return self.app_key, self.invoke_key, self.update_time, page_size, self.query_time, page_index

    def clean_response(self, response):
        users = []

        for info in response:
            user = self._clean_user(info)
            if not user:
                continue
            users.append(user)
        return users

    _FIELDS = [
        "userCompany",
        "userDepart",
        "userDuty",
        "userEmail",
        "userGender",
        "userId",
        "userName",
        "userPreferredMobile",
        "userStatus",
        "userTelephoneNumber",
        "userUid",
        "userUsertype",
    ]

    @classmethod
    def _clean_user(cls, info):
        try:
            user = {field: getattr(info, field, None) for field in cls._FIELDS}
        except Exception as e:
            log.warning(f"Clean user({info}) error({e}) ")
        else:
            return user
