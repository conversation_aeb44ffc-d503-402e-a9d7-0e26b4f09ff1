import logging

from ecitic_ldap.clients.owner import EciticLDAPOwnerClient

log = logging.getLogger()


class EciticLDAPDepartmentClient(EciticLDAPOwnerClient):
    @property
    def method_name(self):
        return "getOrganization"

    _FIELDS = [
        "createUserId",
        "operateState",
        "orgCode",
        "orgCompid",
        "orgDescription",
        "orgDisplayOrder",
        "orgDisplayOrderOa",
        "orgExtensions",
        "orgId",
        "orgInitials",
        "orgLayerCode",
        "orgManagerUid",
        "orgName",
        "orgParentId",
        "orgStatus",
        "orgType",
        "updateUserId",
    ]
