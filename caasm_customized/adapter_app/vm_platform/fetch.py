from caasm_adapter.util.adapter_fetch import fetch_util
from vm_platform.manage import VmPlatformManager


def find_asset(connection, fetch_type, page_index=0, page_size=1, session=None, condition=None, **kwargs):
    result = VmPlatformManager(connection, session).find_asset(page_index=page_index, page_size=page_size)
    result = fetch_util.build_asset(result, fetch_type)
    return fetch_util.return_success(result)


def get_auth_connection(connection, session=None):
    VmPlatformManager(connection, session).find_asset(page_index=1, page_size=1)


def get_asset_count(connection, session=None, **kwargs):
    return VmPlatformManager(connection, session).get_asset_count()
