name: "vm_platform"
display_name: "虚拟机平台"
description: "中信证券虚拟机平台"
type: "虚拟化平台"
company: "中信证券"
logo: "logo.jpg"
version: "0.1"
builtin: false
priority: 1
properties:
  - "虚拟化平台"

connection:
  - name: address
    type: url
    required: true
    display_name: "地址"
    description: "地址信息"
    validate_rules:
      - name: reg
        error_hint: "地址信息无效，请输入以http或者https开头的地址信息"
        setting:
          reg: '^((http|ftp|https)://)(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,5})*(/[a-zA-Z0-9\&%_\./-~-#]*)?$'

  - name: token
    type: string
    required: true
    display_name: "token"
    description: "token"
    validate_rules:
      - name: length
        error_hint: "token长度必须大于等于2且小于等于500"
        setting:
          min: 2
          max: 500

fetch_setting:
  type: disposable
  point: "vm_platform.fetch:find_asset"
  is_need_test_service: true
  count_point: "vm_platform.fetch:get_asset_count"
  test_auth_point: "vm_platform.fetch:get_auth_connection"
  size: 50
  fetch_type_mapper:
    asset:
      - host


fabric_setting:
  choose_point_mapper: { }

merge_setting:
  size: 50
  setting: { }

convert_setting:
  size: 50
  before_executor_mapper: { }
  executor_mapper: { }