from vm_platform.clients.host import VmPlatformHostClient
from vm_platform.clients.host_total import VmPlatformHostTotalClient


class ClientType:
    HOST = "host"
    TOTAL = "total"


class VmPlatformManager(object):
    _CLIENT_MAPPER = {ClientType.HOST: VmPlatformHostClient, ClientType.TOTAL: VmPlatformHostTotalClient}

    def __init__(self, connection, session):
        self._connection = connection
        self._session = session

    def get_asset_count(self):
        try:
            count = self._call(ClientType.TOTAL, page_index=1, page_size=1)
            return count
        except Exception as e:
            return 0

    def find_asset(self, page_index=None, page_size=None):
        """
        获取资产数量
        """
        return self._call(ClientType.HOST, page_index=page_index, page_size=page_size)

    def _call(self, client_type, *args, **kwargs):
        return self._CLIENT_MAPPER[client_type](self._connection, self._session).handle(*args, **kwargs)
