{"canvas": {"nodes": [{"id": "root", "field": "根节点", "path": "root", "datatype": "object", "type": "asset", "level": 0, "sub_fields": [], "x": 0, "y": 0, "asset_type": "asset", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 100, "id": "root.id", "level": 1, "path": "id", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "id", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 200, "id": "root.ip", "level": 1, "path": "ip", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "ip", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 300, "id": "root.discribe", "level": 1, "path": "discribe", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "discribe", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 400, "id": "root.phy_ID", "level": 1, "path": "phy_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "phy_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 500, "id": "root.vc", "level": 1, "path": "vc", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vc", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 600, "id": "root.cluster_ID", "level": 1, "path": "cluster_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cluster_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 700, "id": "root.cluster_NAME", "level": 1, "path": "cluster_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cluster_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 800, "id": "root.vm_NAME", "level": 1, "path": "vm_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 900, "id": "root.vm_ID", "level": 1, "path": "vm_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1000, "id": "root.phy_MACHINE_NAME", "level": 1, "path": "phy_MACHINE_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "phy_MACHINE_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1100, "id": "root.cpu", "level": 1, "path": "cpu", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cpu", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1200, "id": "root.os", "level": 1, "path": "os", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "os", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1300, "id": "root.app_ID", "level": 1, "path": "app_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "app_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1400, "id": "root.app_NAME", "level": 1, "path": "app_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "app_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1500, "id": "root.app_TYPE_ID", "level": 1, "path": "app_TYPE_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "app_TYPE_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1600, "id": "root.deadline", "level": 1, "path": "deadline", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "deadline", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1700, "id": "root.admin_EMAIL", "level": 1, "path": "admin_EMAIL", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "admin_EMAIL", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1800, "id": "root.city", "level": 1, "path": "city", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "city", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 1900, "id": "root.vm_USE", "level": 1, "path": "vm_USE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_USE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2000, "id": "root.use", "level": 1, "path": "use", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "use", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2100, "id": "root.admin_NAME", "level": 1, "path": "admin_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "admin_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2200, "id": "root.resource_TYPE", "level": 1, "path": "resource_TYPE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "resource_TYPE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2300, "id": "root.re_NAME", "level": 1, "path": "re_NAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "re_NAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2400, "id": "root.state", "level": 1, "path": "state", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "state", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2500, "id": "root.memory", "level": 1, "path": "memory", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "memory", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2600, "id": "root.disk", "level": 1, "path": "disk", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "disk", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2700, "id": "root.portgroup", "level": 1, "path": "portgroup", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "portgroup", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2800, "id": "root.vm_HOSTNAME", "level": 1, "path": "vm_HOSTNAME", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_HOSTNAME", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 2900, "id": "root.vm_PASSWORD", "level": 1, "path": "vm_PASSWORD", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_PASSWORD", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3000, "id": "root.vm_ADMIN", "level": 1, "path": "vm_ADMIN", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_ADMIN", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3100, "id": "root.safety", "level": 1, "path": "safety", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "safety", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3200, "id": "root.admin_PHONE", "level": 1, "path": "admin_PHONE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "admin_PHONE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3300, "id": "root.admin_ID", "level": 1, "path": "admin_ID", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "admin_ID", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3400, "id": "root.functional_ZONE", "level": 1, "path": "functional_ZONE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "functional_ZONE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3500, "id": "root.cost_CENTER", "level": 1, "path": "cost_CENTER", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "cost_CENTER", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3600, "id": "root.host_MODE", "level": 1, "path": "host_MODE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "host_MODE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3700, "id": "root.vm_SYS_IP", "level": 1, "path": "vm_SYS_IP", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_SYS_IP", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3800, "id": "root.os_TYPE", "level": 1, "path": "os_TYPE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "os_TYPE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 3900, "id": "root.vm_NOTES", "level": 1, "path": "vm_NOTES", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_NOTES", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4000, "id": "root.vm_STORAGE", "level": 1, "path": "vm_STORAGE", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_STORAGE", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4100, "id": "root.vm_REMARK", "level": 1, "path": "vm_REMARK", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "vm_REMARK", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"x": 200, "y": 4200, "id": "root.new_DISK", "level": 1, "path": "new_DISK", "sub_fields": [], "type": "asset", "datatype": "string", "asset_type": "asset", "field": "new_DISK", "asset_web_type": "动作类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root._7-q98q-Y", "x": 226.517578125, "y": -428.12109375, "order": 2, "level": 1, "source_type": "object", "path": "root._7-q98q-Y", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_email", "values": ["${admin_EMAIL}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root._LU0DUFAY", "x": 224.517578125, "y": -553.12109375, "order": 3, "level": 1, "source_type": "object", "path": "root._LU0DUFAY", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_phone", "values": ["${admin_PHONE}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.IlFlLWq65", "x": 224.517578125, "y": -491.12109375, "order": 4, "level": 1, "source_type": "object", "path": "root.IlFlLWq65", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_departments", "values": ["${cost_CENTER}"], "multi_flag": true}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.hH21wdHLq", "x": 227.517578125, "y": -364.12109375, "order": 7, "level": 1, "source_type": "object", "path": "root.hH21wdHLq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_owmer", "mapping": [{"name": "role", "value": "虚拟机管理员"}, {"name": "username", "value": "${admin_NAME}"}, {"name": "nickname", "value": "${admin_NAME}"}, {"name": "departments", "value": "${_departments}"}, {"name": "phones", "value": "${_phone}"}, {"name": "emails", "value": "${_email}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.hH21wdHLq.UI_nmHsZL", "x": 399.517578125, "y": -368.12109375, "order": 8, "level": 2, "path": "root.hH21wdHLq.UI_nmHsZL", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值", "child_field": "username", "child_is_remove": true}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.hH21wdHLq.UI_nmHsZL.NP5pjncqr", "x": 576.517578125, "y": -369.12109375, "order": 9, "level": 3, "path": "root.hH21wdHLq.UI_nmHsZL.NP5pjncqr", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.owners"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "u6b6GALxD", "display_name": "管理属性", "description": "asset_base.owners", "x": 756.517578125, "y": -368.12109375, "label": "管理属性-责任人", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.E58QGkV3S", "x": 226.517578125, "y": -297.12109375, "order": 10, "level": 1, "source_type": "object", "path": "root.E58QGkV3S", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_ips", "mapping": [{"name": "addr", "value": "${ip}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.E58QGkV3S.qAnByA9KS", "x": 401.517578125, "y": -297.12109375, "order": 11, "level": 2, "path": "root.E58QGkV3S.qAnByA9KS", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "network.ips"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "JtXwYdFLp", "display_name": "网络", "description": "network.ips", "x": 578.517578125, "y": -296.12109375, "label": "网络-IP地址", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.os_TYPE.mvkIM49F-", "x": 774.517578125, "y": 3797.8789062500014, "order": 12, "level": 2, "source_type": "string", "path": "root.os_TYPE.mvkIM49F-", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "30pH821YW", "display_name": "计算机", "description": "computer.os.type", "x": 974.517578125, "y": 3797.8789062500014, "label": "计算机-操作系统-类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.os.-XgP4P47F", "x": 866.517578125, "y": 1195.8789062499989, "order": 13, "level": 2, "source_type": "string", "path": "root.os.-XgP4P47F", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.os.full"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "0Ag6mYYdb", "display_name": "计算机", "description": "computer.os.full", "x": 1066.517578125, "y": 1195.8789062499989, "label": "计算机-操作系统-完整名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.cpu.TChhrA11S", "x": 698.517578125, "y": 1097.87890625, "order": 14, "level": 2, "source_type": "string", "path": "root.cpu.TChhrA11S", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "用于将数据转化为整型", "description": "用于将数据转化为整型", "field": "用于将数据转化为整型", "input": {"asset_web_type": "int", "description": "用于将数据转化为整型"}, "action_type": "int", "attrs": {"text": "用于将数据转化为整型"}}, {"type": "asset", "asset_type": "action", "id": "root.cpu.TChhrA11S.S9gUow_gU", "x": 1109.517578125, "y": 1094.87890625, "order": 15, "level": 3, "path": "root.cpu.TChhrA11S.S9gUow_gU", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.cpu.count"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "XKJ-51bPR", "display_name": "计算机", "description": "computer.hardware.cpu.count", "x": 1309.517578125, "y": 1094.87890625, "label": "计算机-硬件-CPU-个数", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.discribe.FC_zgRBez", "x": 678.517578125, "y": 296.87890625, "order": 19, "level": 2, "source_type": "string", "path": "root.discribe.FC_zgRBez", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "base.description"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "s5ucdFFxh", "display_name": "基础", "description": "base.description", "x": 878.517578125, "y": 296.87890625, "label": "基础-描述信息", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.city.VIoJUwxzF", "x": 785.517578125, "y": 1799.8789062499995, "order": 20, "level": 2, "source_type": "string", "path": "root.city.VIoJUwxzF", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.location.city"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "FTGjAnymO", "display_name": "管理属性", "description": "asset_base.location.city", "x": 985.517578125, "y": 1799.8789062499995, "label": "管理属性-位置-城市", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.memory.LX9eAyI4k", "x": 725.517578125, "y": 2498.87890625, "order": 21, "level": 2, "source_type": "string", "path": "root.memory.LX9eAyI4k", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "用于将数据转化为整型", "description": "用于将数据转化为整型", "field": "用于将数据转化为整型", "input": {"asset_web_type": "int", "description": "用于将数据转化为整型"}, "action_type": "int", "attrs": {"text": "用于将数据转化为整型"}}, {"type": "asset", "asset_type": "action", "id": "root.memory.LX9eAyI4k.dRYSMvcPm", "x": 1195.517578125, "y": 2500.87890625, "order": 22, "level": 3, "path": "root.memory.LX9eAyI4k.dRYSMvcPm", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.mem.capacity"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Q384bWEfK", "display_name": "计算机", "description": "computer.hardware.mem.capacity", "x": 1395.517578125, "y": 2500.87890625, "label": "计算机-硬件-内存-容量", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.fW1bMSdLw", "x": 223.517578125, "y": -615.1210937499991, "order": 23, "level": 1, "source_type": "object", "path": "root.fW1bMSdLw", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_disk", "mapping": [{"name": "capacity", "value": "${disk}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.fW1bMSdLw.ug6wGYvOW", "x": 396.517578125, "y": -614.1210937499991, "order": 24, "level": 2, "path": "root.fW1bMSdLw.ug6wGYvOW", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值", "child_field": "capacity", "child_is_remove": true}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.fW1bMSdLw.ug6wGYvOW.ndqT3TjPc", "x": 574.517578125, "y": -615.1210937499991, "order": 25, "level": 3, "path": "root.fW1bMSdLw.ug6wGYvOW.ndqT3TjPc", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.hardware.disks"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "FspprDjO8", "display_name": "计算机", "description": "computer.hardware.disks", "x": 762.517578125, "y": -617.1210937499991, "label": "计算机-硬件-磁盘", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vm_NAME.f6j1U0Zdk", "x": 825.517578125, "y": 797.8789062500002, "order": 26, "level": 2, "source_type": "string", "path": "root.vm_NAME.f6j1U0Zdk", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.vm.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "pyfHi4AO6", "display_name": "计算机", "description": "computer.vm.name", "x": 1025.517578125, "y": 797.8789062500002, "label": "计算机-虚拟机-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vm_NAME.ltT4131Aq", "x": 823.517578125, "y": 725.8789062500002, "order": 27, "level": 2, "source_type": "string", "path": "root.vm_NAME.ltT4131Aq", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "VTTYM2gp9", "display_name": "管理属性", "description": "asset_base.name", "x": 1023.517578125, "y": 725.8789062500002, "label": "管理属性-资产名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vm_NAME.ECVYYwFbL", "x": 821.517578125, "y": 868.8789062500002, "order": 28, "level": 2, "source_type": "string", "path": "root.vm_NAME.ECVYYwFbL", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.host_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "AAxX-yoTO", "display_name": "计算机", "description": "computer.host_name", "x": 1021.517578125, "y": 868.8789062500002, "label": "计算机-主机名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vm_NAME.WZw1vrBM4", "x": 823.517578125, "y": 947.8789062500002, "order": 29, "level": 2, "source_type": "string", "path": "root.vm_NAME.WZw1vrBM4", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "Rfi5_Co4O", "display_name": "计算机", "description": "computer.name", "x": 1023.517578125, "y": 947.8789062500002, "label": "计算机-名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.kWr01Qn40", "x": 222.517578125, "y": -677.12109375, "order": 31, "level": 1, "source_type": "object", "path": "root.kWr01Qn40", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_vm_type", "values": ["2"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.kWr01Qn40.HsM1tLQoG", "x": 396.517578125, "y": -677.12109375, "order": 32, "level": 2, "path": "root.kWr01Qn40.HsM1tLQoG", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "用于将数据转化为整型", "description": "用于将数据转化为整型", "field": "用于将数据转化为整型", "input": {"asset_web_type": "int", "description": "用于将数据转化为整型"}, "action_type": "int", "attrs": {"text": "用于将数据转化为整型"}}, {"type": "asset", "asset_type": "action", "id": "root.kWr01Qn40.HsM1tLQoG.y6-JXHX7M", "x": 575.517578125, "y": -677.12109375, "order": 33, "level": 3, "path": "root.kWr01Qn40.HsM1tLQoG.y6-JXHX7M", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.vm.type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "uw5do0cUU", "display_name": "计算机", "description": "computer.vm.type", "x": 761.517578125, "y": -681.12109375, "label": "计算机-虚拟机-类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.vm_ID.oBwjCSoAH", "x": 820.517578125, "y": 1018.87890625, "order": 34, "level": 2, "source_type": "string", "path": "root.vm_ID.oBwjCSoAH", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.vm.sid"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "kFgG0IxA1", "display_name": "计算机", "description": "computer.vm.sid", "x": 1020.517578125, "y": 1018.87890625, "label": "计算机-虚拟机-Id", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.os_TYPE.EPH63PrNo", "x": 773.517578125, "y": 3880.8789062499995, "order": 35, "level": 2, "source_type": "string", "path": "root.os_TYPE.EPH63PrNo", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.vm.os_type"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "cnyQUAIeK", "display_name": "计算机", "description": "computer.vm.os_type", "x": 973.517578125, "y": 3880.8789062499995, "label": "计算机-虚拟机-配置OS类型", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.resource_TYPE.EX3WdJtRX", "x": 706.5175781249999, "y": 2196.87890625, "order": 36, "level": 2, "source_type": "string", "path": "root.resource_TYPE.EX3WdJtRX", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "computer.vm.platform_name"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "U5LR9wvKc", "display_name": "计算机", "description": "computer.vm.platform_name", "x": 906.5175781249999, "y": 2196.87890625, "label": "计算机-虚拟机-虚拟化平台名称", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}, {"type": "asset", "asset_type": "action", "id": "root.lE1-HfE5_", "x": 233.9617919921875, "y": -225.16624450683594, "order": 1, "level": 1, "source_type": "object", "path": "root.lE1-HfE5_", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "新增字段", "description": "新增字段，字段值必须是字符串类型", "field": "新增字段", "input": {"asset_web_type": "add", "description": "新增字段，字段值必须是字符串类型", "field": "_app_name", "values": ["${app_NAME}"]}, "action_type": "add", "attrs": {"text": "新增字段"}}, {"type": "asset", "asset_type": "action", "id": "root.lE1-HfE5_.S0sGohJWf", "x": 407.9617919921875, "y": -226.16624450683594, "order": 2, "level": 2, "path": "root.lE1-HfE5_.S0sGohJWf", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "替换(模糊)", "description": "替换指定内容", "field": "替换(模糊)", "input": {"asset_web_type": "fuzzy_replace", "description": "替换指定内容", "src_value": "C.*-"}, "action_type": "fuzzy_replace", "attrs": {"text": "替换(模糊)"}}, {"type": "asset", "asset_type": "action", "id": "root.djRceiFvY", "x": 239.9617919921875, "y": -149.16624450683594, "order": 4, "level": 1, "source_type": "object", "path": "root.djRceiFvY", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "生成对象数组", "description": "生成对象数组", "field": "生成对象数组", "input": {"asset_web_type": "add_list_object", "description": "生成对象数组", "size": "1", "field": "_apps", "mapping": [{"name": "name", "value": "${_app_name}"}, {"name": "full_name", "value": "${_app_name}"}, {"name": "description", "value": "${discribe}"}]}, "action_type": "add_list_object", "attrs": {"text": "生成对象数组"}}, {"type": "asset", "asset_type": "action", "id": "root.djRceiFvY.yg1Q5EYJh", "x": 421.9617919921875, "y": -151.16624450683594, "order": 5, "level": 2, "path": "root.djRceiFvY.yg1Q5EYJh", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "空值过滤器", "description": "过滤空值", "field": "空值过滤器", "input": {"asset_web_type": "filter_empty", "description": "过滤空值", "child_field": "name", "child_is_remove": true}, "action_type": "filter_empty", "attrs": {"text": "空值过滤器"}}, {"type": "asset", "asset_type": "action", "id": "root.djRceiFvY.yg1Q5EYJh.TCGXUNH5O", "x": 606.9617919921875, "y": -152.16624450683594, "order": 6, "level": 3, "path": "root.djRceiFvY.yg1Q5EYJh.TCGXUNH5O", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}, "display_name": "入库", "description": "用于字段入库", "field": "入库", "input": {"asset_web_type": "enter", "description": "用于字段入库", "values": "asset_base.businesses"}, "action_type": "enter", "attrs": {"text": "入库"}}, {"asset_type": "target", "id": "xHezf_xwP", "display_name": "管理属性", "description": "asset_base.businesses", "x": 806.9617919921875, "y": -152.16624450683594, "label": "管理属性-业务系统", "size": [100, 50], "link_points": {"size": 5, "fill": "#fff", "stroke": "black"}, "type": "asset", "z_index": 10, "style": {"hover": {"fill": "#d3adf7"}, "selected": {"fill": "steelblue"}}}], "edges": [{"source": "root", "target": "root.id", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83949171182956791701337814570", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 100, "anchor_index": 0}}, {"source": "root", "target": "root.ip", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44630838794205841701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 200, "anchor_index": 0}}, {"source": "root", "target": "root.discribe", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.79230540175465621701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 300, "anchor_index": 0}}, {"source": "root", "target": "root.phy_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68533636252626231701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 400, "anchor_index": 0}}, {"source": "root", "target": "root.vc", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73265574786969761701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 500, "anchor_index": 0}}, {"source": "root", "target": "root.cluster_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.20576180532523481701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 600, "anchor_index": 0}}, {"source": "root", "target": "root.cluster_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.27989074984579031701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 700, "anchor_index": 0}}, {"source": "root", "target": "root.vm_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.469745760454986171701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 800, "anchor_index": 0}}, {"source": "root", "target": "root.vm_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68799085159774221701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 900, "anchor_index": 0}}, {"source": "root", "target": "root.phy_MACHINE_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.82304085550233541701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1000, "anchor_index": 0}}, {"source": "root", "target": "root.cpu", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.48851050418638241701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1100, "anchor_index": 0}}, {"source": "root", "target": "root.os", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.93634584547865021701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1200, "anchor_index": 0}}, {"source": "root", "target": "root.app_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0563430324390088441701337814571", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1300, "anchor_index": 0}}, {"source": "root", "target": "root.app_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.67689362753428231701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1400, "anchor_index": 0}}, {"source": "root", "target": "root.app_TYPE_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.401314469796969051701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1500, "anchor_index": 0}}, {"source": "root", "target": "root.deadline", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.070790511518419311701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1600, "anchor_index": 0}}, {"source": "root", "target": "root.admin_EMAIL", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63766549137274181701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1700, "anchor_index": 0}}, {"source": "root", "target": "root.city", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.55357137163783571701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1800, "anchor_index": 0}}, {"source": "root", "target": "root.vm_USE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.489582220029689541701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 1900, "anchor_index": 0}}, {"source": "root", "target": "root.use", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87940181128870561701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2000, "anchor_index": 0}}, {"source": "root", "target": "root.admin_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.89961284129902051701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2100, "anchor_index": 0}}, {"source": "root", "target": "root.resource_TYPE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68610211125835031701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2200, "anchor_index": 0}}, {"source": "root", "target": "root.re_NAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6609077308539691701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2300, "anchor_index": 0}}, {"source": "root", "target": "root.state", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.26528778927736221701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2400, "anchor_index": 0}}, {"source": "root", "target": "root.memory", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.52211921636119671701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2500, "anchor_index": 0}}, {"source": "root", "target": "root.disk", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.6536401962310491701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2600, "anchor_index": 0}}, {"source": "root", "target": "root.portgroup", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.125540615463388021701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2700, "anchor_index": 0}}, {"source": "root", "target": "root.vm_HOSTNAME", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.24858578705705271701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2800, "anchor_index": 0}}, {"source": "root", "target": "root.vm_PASSWORD", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.54781992162655221701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 2900, "anchor_index": 0}}, {"source": "root", "target": "root.vm_ADMIN", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.148378442221233531701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3000, "anchor_index": 0}}, {"source": "root", "target": "root.safety", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.057990867225993671701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3100, "anchor_index": 0}}, {"source": "root", "target": "root.admin_PHONE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96795056617669871701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3200, "anchor_index": 0}}, {"source": "root", "target": "root.admin_ID", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.91528786125823821701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3300, "anchor_index": 0}}, {"source": "root", "target": "root.functional_ZONE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.344262036368191641701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3400, "anchor_index": 0}}, {"source": "root", "target": "root.cost_CENTER", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.24477440716885181701337814572", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3500, "anchor_index": 0}}, {"source": "root", "target": "root.host_MODE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0383409780197467851701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3600, "anchor_index": 0}}, {"source": "root", "target": "root.vm_SYS_IP", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.078708545943486421701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3700, "anchor_index": 0}}, {"source": "root", "target": "root.os_TYPE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.11816787892671511701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3800, "anchor_index": 0}}, {"source": "root", "target": "root.vm_NOTES", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.188803819101003971701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 3900, "anchor_index": 0}}, {"source": "root", "target": "root.vm_STORAGE", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.69534833547274591701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4000, "anchor_index": 0}}, {"source": "root", "target": "root.vm_REMARK", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.99068111171468361701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4100, "anchor_index": 0}}, {"source": "root", "target": "root.new_DISK", "source_anchor": 0, "target_anchor": 0, "type": "step-line", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68502889608718891701337814573", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}}, "start_point": {"x": 0, "y": 25.5, "anchor_index": 0}, "end_point": {"x": 149.5, "y": 4200, "anchor_index": 0}}, {"source": "root", "target": "root._7-q98q-Y", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.84967128555604471701337956906", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 176.017578125, "y": -428.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root._LU0DUFAY", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.73425664802309121701337984780", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 174.017578125, "y": -553.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.IlFlLWq65", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.373202725570431551701338250279", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 174.017578125, "y": -491.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root", "target": "root.hH21wdHLq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76388840787454071701338311129", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 177.017578125, "y": -364.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.hH21wdHLq", "target": "root.hH21wdHLq.UI_nmHsZL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.024717290871327571701338468791", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 278.017578125, "y": -364.12109375, "anchor_index": 1}, "end_point": {"x": 349.017578125, "y": -368.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.hH21wdHLq.UI_nmHsZL", "target": "root.hH21wdHLq.UI_nmHsZL.NP5pjncqr", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.77093010882866151701338484446", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 450.017578125, "y": -368.12109375, "anchor_index": 1}, "end_point": {"x": 526.017578125, "y": -369.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.hH21wdHLq.UI_nmHsZL.NP5pjncqr", "target": "u6b6GALxD", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.99670178416451271701338493709", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 627.017578125, "y": -369.12109375, "anchor_index": 1}, "end_point": {"x": 706.017578125, "y": -368.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.E58QGkV3S", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.96326163996143331701338511592", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 176.017578125, "y": -297.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.E58QGkV3S", "target": "root.E58QGkV3S.qAnByA9KS", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76114113522357731701338641428", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 277.017578125, "y": -297.12109375, "anchor_index": 1}, "end_point": {"x": 351.017578125, "y": -297.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.E58QGkV3S.qAnByA9KS", "target": "JtXwYdFLp", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.62914198529002021701338649587", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 452.017578125, "y": -297.12109375, "anchor_index": 1}, "end_point": {"x": 528.017578125, "y": -296.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.os_TYPE", "target": "root.os_TYPE.mvkIM49F-", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.184054090329926371701338731404", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3800, "anchor_index": 1}, "end_point": {"x": 724.017578125, "y": 3797.8789062500014, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.os_TYPE.mvkIM49F-", "target": "30pH821YW", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.248820609821857941701338739704", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 825.017578125, "y": 3797.8789062500014, "anchor_index": 1}, "end_point": {"x": 924.017578125, "y": 3797.8789062500014, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.os", "target": "root.os.-XgP4P47F", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63581091412201431701338756158", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1200, "anchor_index": 1}, "end_point": {"x": 816.017578125, "y": 1195.8789062499989, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.os.-XgP4P47F", "target": "0Ag6mYYdb", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.78785273354859681701338764576", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 917.017578125, "y": 1195.8789062499989, "anchor_index": 1}, "end_point": {"x": 1016.017578125, "y": 1195.8789062499989, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.cpu", "target": "root.cpu.TChhrA11S", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.83744651579598361701338839278", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1100, "anchor_index": 1}, "end_point": {"x": 648.017578125, "y": 1097.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cpu.TChhrA11S", "target": "root.cpu.TChhrA11S.S9gUow_gU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.245520732260273271701338857583", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 749.017578125, "y": 1097.87890625, "anchor_index": 1}, "end_point": {"x": 1059.017578125, "y": 1094.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.cpu.TChhrA11S.S9gUow_gU", "target": "XKJ-51bPR", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.28565964874206461701338866496", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1160.017578125, "y": 1094.87890625, "anchor_index": 1}, "end_point": {"x": 1259.017578125, "y": 1094.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.discribe", "target": "root.discribe.FC_zgRBez", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.0437647117018393761701339122552", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 300, "anchor_index": 1}, "end_point": {"x": 628.017578125, "y": 296.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.discribe.FC_zgRBez", "target": "s5ucdFFxh", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.58804673990670221701339130598", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 729.017578125, "y": 296.87890625, "anchor_index": 1}, "end_point": {"x": 828.017578125, "y": 296.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.city", "target": "root.city.VIoJUwxzF", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94883361105280421701339167007", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 1800, "anchor_index": 1}, "end_point": {"x": 735.017578125, "y": 1799.8789062499995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.city.VIoJUwxzF", "target": "FTGjAnymO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.082856710713878231701339176807", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 836.017578125, "y": 1799.8789062499995, "anchor_index": 1}, "end_point": {"x": 935.017578125, "y": 1799.8789062499995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.memory", "target": "root.memory.LX9eAyI4k", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.45910668383623831701339207930", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2500, "anchor_index": 1}, "end_point": {"x": 675.017578125, "y": 2498.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.memory.LX9eAyI4k", "target": "root.memory.LX9eAyI4k.dRYSMvcPm", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.271361061456424141701339220940", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 776.017578125, "y": 2498.87890625, "anchor_index": 1}, "end_point": {"x": 1145.017578125, "y": 2500.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.memory.LX9eAyI4k.dRYSMvcPm", "target": "Q384bWEfK", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97764351870770221701339233668", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 1246.017578125, "y": 2500.87890625, "anchor_index": 1}, "end_point": {"x": 1345.017578125, "y": 2500.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.fW1bMSdLw", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.479917791991946841701339280966", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 173.017578125, "y": -615.1210937499991, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.fW1bMSdLw", "target": "root.fW1bMSdLw.ug6wGYvOW", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.29038158163448661701339319813", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 274.017578125, "y": -615.1210937499991, "anchor_index": 1}, "end_point": {"x": 346.017578125, "y": -614.1210937499991, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.fW1bMSdLw.ug6wGYvOW", "target": "root.fW1bMSdLw.ug6wGYvOW.ndqT3TjPc", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.266840485912537731701339340308", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 447.017578125, "y": -614.1210937499991, "anchor_index": 1}, "end_point": {"x": 524.017578125, "y": -615.1210937499991, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.fW1bMSdLw.ug6wGYvOW.ndqT3TjPc", "target": "FspprDjO8", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.53493341265138581701339352166", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 625.017578125, "y": -615.1210937499991, "anchor_index": 1}, "end_point": {"x": 712.017578125, "y": -617.1210937499991, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.vm_NAME", "target": "root.vm_NAME.f6j1U0Zdk", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.80780610763927711701339408425", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 800, "anchor_index": 1}, "end_point": {"x": 775.017578125, "y": 797.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.vm_NAME.f6j1U0Zdk", "target": "pyfHi4AO6", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.97964305759454921701339433843", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 876.017578125, "y": 797.8789062500002, "anchor_index": 1}, "end_point": {"x": 975.017578125, "y": 797.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_NAME", "target": "root.vm_NAME.ltT4131Aq", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.76702776525645971701339456166", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 800, "anchor_index": 1}, "end_point": {"x": 773.017578125, "y": 725.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.vm_NAME.ltT4131Aq", "target": "VTTYM2gp9", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.073547646411871611701339467284", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 874.017578125, "y": 725.8789062500002, "anchor_index": 1}, "end_point": {"x": 973.017578125, "y": 725.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_NAME", "target": "root.vm_NAME.ECVYYwFbL", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.141145207240116031701339470721", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 800, "anchor_index": 1}, "end_point": {"x": 771.017578125, "y": 868.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_NAME.ECVYYwFbL", "target": "AAxX-yoTO", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.13200955658714531701339490595", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 872.017578125, "y": 868.8789062500002, "anchor_index": 1}, "end_point": {"x": 971.017578125, "y": 868.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_NAME", "target": "root.vm_NAME.WZw1vrBM4", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.90078327254195181701339493508", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 800, "anchor_index": 1}, "end_point": {"x": 773.017578125, "y": 947.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_NAME.WZw1vrBM4", "target": "Rfi5_Co4O", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.61763357961835451701339509258", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 874.017578125, "y": 947.8789062500002, "anchor_index": 1}, "end_point": {"x": 973.017578125, "y": 947.8789062500002, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.kWr01Qn40", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.8245316633085581701339586483", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 172.017578125, "y": -677.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.kWr01Qn40", "target": "root.kWr01Qn40.HsM1tLQoG", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.77772251096631821701339614539", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 273.017578125, "y": -677.12109375, "anchor_index": 1}, "end_point": {"x": 346.017578125, "y": -677.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.kWr01Qn40.HsM1tLQoG", "target": "root.kWr01Qn40.HsM1tLQoG.y6-JXHX7M", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.98153261511557411701339623080", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 447.017578125, "y": -677.12109375, "anchor_index": 1}, "end_point": {"x": 525.017578125, "y": -677.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.kWr01Qn40.HsM1tLQoG.y6-JXHX7M", "target": "uw5do0cUU", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.5782952761771781701339634879", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 626.017578125, "y": -677.12109375, "anchor_index": 1}, "end_point": {"x": 711.017578125, "y": -681.12109375, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.vm_ID", "target": "root.vm_ID.oBwjCSoAH", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.63944365330780181701339659819", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 900, "anchor_index": 1}, "end_point": {"x": 770.017578125, "y": 1018.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.vm_ID.oBwjCSoAH", "target": "kFgG0IxA1", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.92765084420125611701339668144", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 871.017578125, "y": 1018.87890625, "anchor_index": 1}, "end_point": {"x": 970.017578125, "y": 1018.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.os_TYPE", "target": "root.os_TYPE.EPH63PrNo", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.49750043310078641701339740000", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 3800, "anchor_index": 1}, "end_point": {"x": 723.017578125, "y": 3880.8789062499995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.os_TYPE.EPH63PrNo", "target": "cnyQUAIeK", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.94027025061205641701339747676", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 824.017578125, "y": 3880.8789062499995, "anchor_index": 1}, "end_point": {"x": 923.017578125, "y": 3880.8789062499995, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.resource_TYPE", "target": "root.resource_TYPE.EX3WdJtRX", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.078232700337174781701339818386", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 250.5, "y": 2200, "anchor_index": 1}, "end_point": {"x": 656.0175781249999, "y": 2196.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.resource_TYPE.EX3WdJtRX", "target": "U5LR9wvKc", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.287661767893559351701339830240", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 757.0175781249999, "y": 2196.87890625, "anchor_index": 1}, "end_point": {"x": 856.0175781249999, "y": 2196.87890625, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.lE1-HfE5_", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.169066742739813371705377379836", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 183.4617919921875, "y": -225.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.lE1-HfE5_", "target": "root.lE1-HfE5_.S0sGohJWf", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.87332117694956121705377415403", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 284.4617919921875, "y": -225.16624450683594, "anchor_index": 1}, "end_point": {"x": 357.4617919921875, "y": -226.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root", "target": "root.djRceiFvY", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.44226397188930291705377457703", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 50.5, "y": 0, "anchor_index": 1}, "end_point": {"x": 189.4617919921875, "y": -149.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}, {"source": "root.djRceiFvY", "target": "root.djRceiFvY.yg1Q5EYJh", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.68642530460643041705377498819", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 290.4617919921875, "y": -149.16624450683594, "anchor_index": 1}, "end_point": {"x": 371.4617919921875, "y": -151.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.djRceiFvY.yg1Q5EYJh", "target": "root.djRceiFvY.yg1Q5EYJh.TCGXUNH5O", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.246834560944006581705377509802", "style": {"active": {"stroke": "rgb(95, 149, 255)", "line_width": 1}, "selected": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "shadow_color": "rgb(95, 149, 255)", "shadow_blur": 10, "text-shape": {"font_weight": 500}}, "highlight": {"stroke": "rgb(95, 149, 255)", "line_width": 2, "text-shape": {"font_weight": 500}}, "inactive": {"stroke": "rgb(234, 234, 234)", "line_width": 1}, "disable": {"stroke": "rgb(245, 245, 245)", "line_width": 1}, "stroke": "#d9d9d9"}, "start_point": {"x": 472.4617919921875, "y": -151.16624450683594, "anchor_index": 1}, "end_point": {"x": 556.4617919921875, "y": -152.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0], "label_cfg": {"style": {"fill": "#555"}}}, {"source": "root.djRceiFvY.yg1Q5EYJh.TCGXUNH5O", "target": "xHezf_xwP", "type": "cubic-horizontal", "size": 3, "color": "#d9d9d9", "z_index": -1, "id": "edge-0.454236517109830151705377516117", "style": {}, "start_point": {"x": 657.4617919921875, "y": -152.16624450683594, "anchor_index": 1}, "end_point": {"x": 756.4617919921875, "y": -152.16624450683594, "anchor_index": 0}, "curve_position": [0.5, 0.5], "min_curve_offset": [0, 0]}]}, "rules": [{"name": "add", "setting": {"field": "_email", "values": ["${admin_EMAIL}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root._7-q98q-Y"}, {"name": "add", "setting": {"field": "_phone", "values": ["${admin_PHONE}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root._LU0DUFAY"}, {"name": "add", "setting": {"field": "_departments", "values": ["${cost_CENTER}"], "multi_flag": true, "is_output_root": null}, "sub_rules": [], "_id": "root.IlFlLWq65"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_owmer", "mapping": [{"name": "role", "value": "虚拟机管理员"}, {"name": "username", "value": "${admin_NAME}"}, {"name": "nickname", "value": "${admin_NAME}"}, {"name": "departments", "value": "${_departments}"}, {"name": "phones", "value": "${_phone}"}, {"name": "emails", "value": "${_email}"}]}, "sub_rules": [], "_id": "root.hH21wdHLq"}, {"name": "filter_empty", "setting": {"child_field": "username", "child_is_remove": true, "field": "_owmer"}, "sub_rules": [], "_id": "root.hH21wdHLq.UI_nmHsZL"}, {"name": "enter", "setting": {"values": "asset_base.owners", "field": "${_owmer}"}, "sub_rules": [], "_id": "root.hH21wdHLq.UI_nmHsZL.NP5pjncqr"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_ips", "mapping": [{"name": "addr", "value": "${ip}"}]}, "sub_rules": [], "_id": "root.E58QGkV3S"}, {"name": "enter", "setting": {"values": "network.ips", "field": "${_ips}"}, "sub_rules": [], "_id": "root.E58QGkV3S.qAnByA9KS"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_disk", "mapping": [{"name": "capacity", "value": "${disk}"}]}, "sub_rules": [], "_id": "root.fW1bMSdLw"}, {"name": "filter_empty", "setting": {"child_field": "capacity", "child_is_remove": true, "field": "_disk"}, "sub_rules": [], "_id": "root.fW1bMSdLw.ug6wGYvOW"}, {"name": "enter", "setting": {"values": "computer.hardware.disks", "field": "${_disk}"}, "sub_rules": [], "_id": "root.fW1bMSdLw.ug6wGYvOW.ndqT3TjPc"}, {"name": "add", "setting": {"field": "_vm_type", "values": [2], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.kWr01Qn40"}, {"name": "int", "setting": {"field": "_vm_type"}, "sub_rules": [], "_id": "root.kWr01Qn40.HsM1tLQoG"}, {"name": "enter", "setting": {"values": "computer.vm.type", "field": "${_vm_type}"}, "sub_rules": [], "_id": "root.kWr01Qn40.HsM1tLQoG.y6-JXHX7M"}, {"name": "add", "setting": {"field": "_app_name", "values": ["${app_NAME}"], "multi_flag": null, "is_output_root": null}, "sub_rules": [], "_id": "root.lE1-HfE5_"}, {"name": "fuzzy_replace", "setting": {"src_value": "C.*-", "dst_value": null, "field": "_app_name"}, "sub_rules": [], "_id": "root.lE1-HfE5_.S0sGohJWf"}, {"name": "add_list_object", "setting": {"size": 1, "field": "_apps", "mapping": [{"name": "name", "value": "${_app_name}"}, {"name": "full_name", "value": "${_app_name}"}, {"name": "description", "value": "${discribe}"}]}, "sub_rules": [], "_id": "root.djRceiFvY"}, {"name": "filter_empty", "setting": {"child_field": "name", "child_is_remove": true, "field": "_apps"}, "sub_rules": [], "_id": "root.djRceiFvY.yg1Q5EYJh"}, {"name": "enter", "setting": {"values": "asset_base.businesses", "field": "${_apps}"}, "sub_rules": [], "_id": "root.djRceiFvY.yg1Q5EYJh.TCGXUNH5O"}, {"name": "enter", "setting": {"values": "base.description", "field": "${discribe}"}, "sub_rules": [], "_id": "root.discribe.FC_zgRBez"}, {"name": "enter", "setting": {"values": "computer.vm.name", "field": "${vm_NAME}"}, "sub_rules": [], "_id": "root.vm_NAME.f6j1U0Zdk"}, {"name": "enter", "setting": {"values": "asset_base.name", "field": "${vm_NAME}"}, "sub_rules": [], "_id": "root.vm_NAME.ltT4131Aq"}, {"name": "enter", "setting": {"values": "computer.host_name", "field": "${vm_NAME}"}, "sub_rules": [], "_id": "root.vm_NAME.ECVYYwFbL"}, {"name": "enter", "setting": {"values": "computer.name", "field": "${vm_NAME}"}, "sub_rules": [], "_id": "root.vm_NAME.WZw1vrBM4"}, {"name": "enter", "setting": {"values": "computer.vm.sid", "field": "${vm_ID}"}, "sub_rules": [], "_id": "root.vm_ID.oBwjCSoAH"}, {"name": "int", "setting": {"field": "cpu"}, "sub_rules": [], "_id": "root.cpu.TChhrA11S"}, {"name": "enter", "setting": {"values": "computer.hardware.cpu.count", "field": "${cpu}"}, "sub_rules": [], "_id": "root.cpu.TChhrA11S.S9gUow_gU"}, {"name": "enter", "setting": {"values": "computer.os.full", "field": "${os}"}, "sub_rules": [], "_id": "root.os.-XgP4P47F"}, {"name": "enter", "setting": {"values": "asset_base.location.city", "field": "${city}"}, "sub_rules": [], "_id": "root.city.VIoJUwxzF"}, {"name": "enter", "setting": {"values": "computer.vm.platform_name", "field": "${resource_TYPE}"}, "sub_rules": [], "_id": "root.resource_TYPE.EX3WdJtRX"}, {"name": "int", "setting": {"field": "memory"}, "sub_rules": [], "_id": "root.memory.LX9eAyI4k"}, {"name": "enter", "setting": {"values": "computer.hardware.mem.capacity", "field": "${memory}"}, "sub_rules": [], "_id": "root.memory.LX9eAyI4k.dRYSMvcPm"}, {"name": "enter", "setting": {"values": "computer.os.type", "field": "${os_TYPE}"}, "sub_rules": [], "_id": "root.os_TYPE.mvkIM49F-"}, {"name": "enter", "setting": {"values": "computer.vm.os_type", "field": "${os_TYPE}"}, "sub_rules": [], "_id": "root.os_TYPE.EPH63PrNo"}], "adapter_name": "vm_platform", "fetch_type": "host", "model_name": "computer", "asset_type": "host", "internal": true}