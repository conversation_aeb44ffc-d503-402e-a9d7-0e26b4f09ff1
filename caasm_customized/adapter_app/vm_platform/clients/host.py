from vm_platform.clients.base import VmPlatformBaseClient


class VmPlatformHostClient(VmPlatformBaseClient):
    URL = "/api/omm/vm/list"
    METHOD = "post"

    def build_request_json(self, page_index=None, page_size=None):
        return {"pageNum": page_index, "pageSize": page_size, "roleId": 0, "toDnUser": "031851"}

    @property
    def data_key_name(self):
        return "data.list"

    @property
    def flag_key_name(self):
        return "code"

    @property
    def success_flag(self):
        return 200
