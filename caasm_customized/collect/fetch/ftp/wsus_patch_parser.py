import datetime

from caasm_file.parsers.csv_parse import CsvFileParserV1
from caasm_ftp.parsers.base import BaseParser
from caasm_tool.constants import DATE_FORMAT_1


class WSUSPatchParser(BaseParser):
    def __init__(self, *args, **kwargs):
        super(WSUSPatchParser, self).__init__(*args, **kwargs)
        self._date = datetime.datetime.now().strftime(DATE_FORMAT_1)
        self._parser = CsvParser()

    def clean(self, content):
        return self._parser.handle_common(content)

    @property
    def file_names(self):
        return ["Beijing.csv", "Shanghai.csv", "Shenzhen.csv", "GuangZhou.csv", "Hangzhou.csv", "Qingdao.csv"]

    @classmethod
    def get_category(cls):
        return "asset"

    @classmethod
    def get_name(cls):
        return "WSUS补丁数据导入"

    @classmethod
    def get_data_type(cls):
        return "terminal"

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_file_dir(cls):
        return "WSUS"


class CsvParser(CsvFileParserV1):
    INVALID_ROW_INDEXES = [0, 1]

    def _parse_name(self, row, result):
        self.restore("ComputerTarget", row[0], result)
        self.restore("NeededCount", row[1], result)
        self.restore("DownloadedCount", row[2], result)
        self.restore("NotInstalledCount", row[3], result)
        self.restore("InstalledCount", row[4], result)
        self.restore("FailedCount", row[5], result)

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_data_type(cls):
        return "terminal"


CLASS = WSUSPatchParser
