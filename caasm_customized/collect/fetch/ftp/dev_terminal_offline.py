import datetime

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1
from caasm_ftp.parsers.base import BaseParser
from caasm_tool.constants import DATE_FORMAT_1


class DevTerminalOfflineParser(BaseParser):
    def __init__(self, *args, **kwargs):
        super(DevTerminalOfflineParser, self).__init__(*args, **kwargs)
        self._date = datetime.datetime.now().strftime(DATE_FORMAT_1)
        self._parser = ExcelParser()

    def clean(self, content):
        return self._parser.handle_common(content)

    @property
    def file_names(self):
        return [f"VDI-{self._date}"]

    @classmethod
    def get_category(cls):
        return "asset"

    @classmethod
    def get_name(cls):
        return "开发VDI离线数据导入"

    @classmethod
    def get_data_type(cls):
        return "terminal"

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_file_dir(cls):
        return ""


class ExcelParser(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0, 1]

    _COMPUTE_NAME_FIELD = "computer.host_name"
    _NAME_FIELD = "base.name"
    _DEVICE_NAME_FIELD = "asset_base.device.name"
    _OWNER_FIELD = "asset_base.owners"
    _IP_FIELD = "network.ips"

    def _parse_name(self, row, result):
        self.restore(self._COMPUTE_NAME_FIELD, row[1], result)
        self.restore(self._NAME_FIELD, row[1], result)
        self.restore(self._DEVICE_NAME_FIELD, row[1], result)

    def _parse_network(self, row, result):
        address = row[2]
        if address:
            ips = [{"addr": address}]
            self.restore(self._IP_FIELD, ips, result)

    def _parse_asset_base(self, row, result):
        owner = row[0]
        if owner:
            self.restore(self._OWNER_FIELD, [{"username": owner, "nickname": owner}], result)

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_data_type(cls):
        return "terminal"


CLASS = DevTerminalOfflineParser
