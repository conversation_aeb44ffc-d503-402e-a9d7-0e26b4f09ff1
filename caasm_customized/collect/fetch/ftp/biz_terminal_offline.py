import datetime

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1
from caasm_ftp.parsers.base import BaseParser
from caasm_tool.constants import DATE_FORMAT_1


class BizTerminalOfflineParser(BaseParser):
    def __init__(self, *args, **kwargs):
        super(BizTerminalOfflineParser, self).__init__(*args, **kwargs)
        self._date = datetime.datetime.now().strftime(DATE_FORMAT_1)
        self._parser = ExcelParser()
        self._file_name = kwargs.get("share_name", None)

    def clean(self, content):
        return self._parser.handle_common(content)

    @property
    def file_names(self):
        if self._file_name:
            return [self._file_name]
        return [f"虚拟机配置报表_{self._date}.xlsx"]

    @classmethod
    def get_category(cls):
        return "asset"

    @classmethod
    def get_name(cls):
        return "业务VDI离线数据导入"

    @classmethod
    def get_data_type(cls):
        return "terminal"

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_file_dir(cls):
        return ""


class ExcelParser(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0, 1]

    _COMPUTE_NAME_FIELD = "computer.host_name"
    _NAME_FIELD = "base.name"
    _DEVICE_NAME_FIELD = "asset_base.device.name"
    _OWNER_FIELD = "asset_base.owners"
    _IP_FIELD = "network.ips"
    _MAC_FIELD = "network.interfaces"

    def _parse_name(self, row, result):
        self.restore(self._COMPUTE_NAME_FIELD, row[1], result)
        self.restore(self._NAME_FIELD, row[1], result)
        self.restore(self._DEVICE_NAME_FIELD, row[1], result)

    def _parse_network(self, row, result):
        address = row[7]
        mac = row[8]

        if address:
            ips = [{"addr": address}]
            if mac:
                ips[0]["mac"] = mac
            self.restore(self._IP_FIELD, ips, result)

        if mac:
            interfaces = [{"mac": mac}]
            self.restore(self._MAC_FIELD, interfaces, result)

    def _parse_asset_base(self, row, result):
        owner = row[4]
        if owner:
            self.restore(self._OWNER_FIELD, [{"username": owner, "nickname": owner}], result)

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_data_type(cls):
        return "terminal"


CLASS = BizTerminalOfflineParser
