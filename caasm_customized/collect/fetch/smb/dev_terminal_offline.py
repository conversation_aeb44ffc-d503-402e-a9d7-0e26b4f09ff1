import io
import re
from datetime import datetime

from smbprotocol.file_info import (
    FileAttributes,
    FileDirectoryInformation,
    FileIdBothDirectoryInformation,
    FileInformationClass,
)
from smbprotocol.open import (
    Open,
    FilePipePrinterAccessMask,
    CreateOptions,
    CreateDisposition,
    DirectoryAccessMask,
    ImpersonationLevel,
    ShareAccess,
)

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1
from caasm_smb.parsers.base import BaseSMBParser


class DevTerminalOfflineParser(BaseSMBParser):
    FILE_KEYWORD_NAME = "VDI-"

    def __init__(self, *args, **kwargs):
        super(DevTerminalOfflineParser, self).__init__(*args, **kwargs)
        self._parser = ExcelParser()

    def get_max_date_file_name(self, file_names):
        filtered_files = [
            (name, datetime.strptime(re.search(r"(\d{4}-\d{2}-\d{2})", name).group(1), "%Y-%m-%d"))
            for name in file_names
            if (self.FILE_KEYWORD_NAME in name) and ("_ip" in name)
        ]

        # 获取日期最大的文件名
        max_date_file = max(filtered_files, key=lambda x: x[1])[0] if filtered_files else None

        if not max_date_file:
            return []
        return [max_date_file]

    def find_all_file_name(self):
        directory = Open(self._smb_tree_connect, self.get_file_dir())
        directory.create(
            desired_access=DirectoryAccessMask.FILE_LIST_DIRECTORY | DirectoryAccessMask.FILE_READ_ATTRIBUTES,
            create_disposition=CreateDisposition.FILE_OPEN,
            create_options=CreateOptions.FILE_DIRECTORY_FILE,
            file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY,
            impersonation_level=ImpersonationLevel.Impersonation,
            share_access=ShareAccess.FILE_SHARE_READ,
        )

        # 列出目录内容
        file_names = []
        for entry in directory.query_directory("*", FileInformationClass.FILE_FULL_DIRECTORY_INFORMATION):
            _file_name = entry["file_name"].get_value()
            file_names.append(_file_name.decode("utf-16-le"))
        return file_names

    def handle(self):
        contents = []
        file_names = self.find_all_file_name()
        for file_name in self.get_max_date_file_name(file_names=file_names):
            file = Open(self._smb_tree_connect, file_name)
            file.create(
                desired_access=FilePipePrinterAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN,
                create_options=CreateOptions.FILE_NON_DIRECTORY_FILE,
                file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL,
                impersonation_level=ImpersonationLevel.Impersonation,
                share_access=ShareAccess.FILE_SHARE_READ,
            )
            writer = file.read(0, file.end_of_file)

            contents.append(writer)
            file.close()

        records = []
        for content in contents:

            records.extend(self.clean(content))

        result = []
        for info in records:
            self.enrich(info)
            result.append(info)
        return result

    def clean(self, content):
        return self._parser.handle_common(content)

    @property
    def file_names(self):
        return []

    @classmethod
    def get_category(cls):
        return "asset"

    @classmethod
    def get_parser_name(cls):
        return "开发VDI离线数据导入"

    @classmethod
    def get_data_type(cls):
        return "terminal"

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_file_dir(cls):
        return ""


class ExcelParser(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0, 1]

    _COMPUTE_NAME_FIELD = "computer.host_name"
    _NAME_FIELD = "base.name"
    _DEVICE_NAME_FIELD = "asset_base.device.name"
    _OWNER_FIELD = "asset_base.owners"
    _IP_FIELD = "network.ips"

    def _parse_name(self, row, result):
        self.restore(self._COMPUTE_NAME_FIELD, row[1], result)
        self.restore(self._NAME_FIELD, row[1], result)
        self.restore(self._DEVICE_NAME_FIELD, row[1], result)

    def _parse_network(self, row, result):
        address = row[2]
        if address:
            ips = [{"addr": address}]
            self.restore(self._IP_FIELD, ips, result)

    def _parse_asset_base(self, row, result):
        owner = row[0]
        if owner:
            self.restore(self._OWNER_FIELD, [{"username": owner, "nickname": owner}], result)

    @classmethod
    def get_model_name(cls):
        return "computer"

    @classmethod
    def get_asset_type(cls, record):
        return "terminal"

    @classmethod
    def get_data_type(cls):
        return "terminal"


CLASS = DevTerminalOfflineParser
