from caasm_tool import log
from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class BusinessProtectionLevelImporter(XlsxFileParserV1):
    """
    There is enforcement on the business magnitude, which has been put on security record. The magnitude has some kind of level, such as 2, 3.
    """

    INVALID_SHEET_INDEXES = [1]
    INVALID_ROW_INDEXES = [0]
    BUSINESS_NAME_FIELD = "business.name"
    BUSINESS_FULLNAME_FIELD = "business.full_name"
    BUSINESS_PROTECTION_LEVEL_FIELD = "business.grade_protection_level"
    BUSINESS_CODE = "business.code"
    BUSINESS_CLASS = "business.class"

    def _parse_name(self, row, result):
        self.restore(self.BUSINESS_NAME_FIELD, row[0], result)

    def _parse_full_name(self, row, result):
        self.restore(self.BUSINESS_FULLNAME_FIELD, row[0], result)

    def _parse_grade_protection_level(self, row, result):
        if len(row) < 3:
            return
        try:
            level_str = row[2]
            if len(level_str) < 1:
                return
            level = int(level_str[0])
        except Exception as e:
            log.info(f"business protection level parse error: {str(e)}")
            return
        self.restore(self.BUSINESS_PROTECTION_LEVEL_FIELD, level, result)

    def _parse_code(self, row, result):
        if len(row) < 2:
            return
        self.restore(self.BUSINESS_CODE, row[1], result)

    def _parse_class(self, row, result):
        if len(row) < 4:
            return
        self.restore(self.BUSINESS_CLASS, row[3], result)

    @staticmethod
    def get_parser_name():
        return "系统安全备案等级导入"

    @staticmethod
    def get_category():
        return "business"

    @staticmethod
    def get_model_name():
        return "business"

    @staticmethod
    def get_data_type():
        return "business"

    @classmethod
    def get_asset_type(cls, record):
        return "business"


CLASS = BusinessProtectionLevelImporter
