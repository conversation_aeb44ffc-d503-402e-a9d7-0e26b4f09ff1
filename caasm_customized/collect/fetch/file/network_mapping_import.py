import copy
import itertools

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class NetworkMappingImport(XlsxFileParserV1):
    _DOMAIN_FIELD = "network_mapping.domain"
    _CLASS_FIELD = "network_mapping.class"
    _ENTRY_IP_FIELD = "network_mapping.entry_ip"
    _ENTRY_PORTS_FIELD = "network_mapping.entry_ports"
    _SYS_NAME_FIELD = "network_mapping.sys_name"
    _DESTINATIONS_FIELD = "network_mapping.destinations"
    _ASSET_OWNER_FIELD = "asset_base.owners"
    _ASSET_BUSINESS_FIELD = "asset_base.businesses"

    def clean(self, data):
        result = {}

        entry_ips = self.extract(data, self._ENTRY_IP_FIELD) or [None]
        domains = self.extract(data, self._DOMAIN_FIELD) or [None]

        for entry_ip, domain in itertools.product(entry_ips, domains):
            result = copy.deepcopy(data)
            self.restore(self._ENTRY_IP_FIELD, entry_ip, result)
            self.restore(self._DOMAIN_FIELD, domain, result)

        return result

    def _parse_class(self, row, result):
        self.restore(self._CLASS_FIELD, "nginx", result)

    def _parse_entry_ip(self, row, result):
        ips = self._entry_parse_core(row, 8, 0, 1)
        self.restore(self._ENTRY_IP_FIELD, ips, result)

    def _parse_entry_ports(self, row, result):
        ips = self._entry_parse_core(row, 8, 1, 2)
        self.restore(self._ENTRY_PORTS_FIELD, ips, result)

    def _parse_destinations(self, row, result):
        data = []
        for dest_info in row[9].split("\n"):
            if not dest_info:
                continue
            dest_ip = dest_port = None

            dest_more = dest_info.split(":")
            dest_more_len = len(dest_more)
            if dest_more_len >= 1:
                dest_ip = dest_more[0]
            if dest_more_len >= 2:
                dest_port = dest_more[1]

            data.append(
                {
                    "ip": dest_ip,
                    "port": dest_port,
                }
            )
        self.restore(self._DESTINATIONS_FIELD, data, result)

    def _parse_sys_name(self, row, result):
        self.restore(self._SYS_NAME_FIELD, row[5], result)

    def _parse_owner(self, row, result):
        data = []
        for owner in row[10].split("/"):
            data.append(
                {
                    "username": owner,
                    "nickname": owner,
                }
            )
        self.restore(self._ASSET_OWNER_FIELD, data, result)

    def _parse_business(self, row, result):
        if not row[5]:
            return
        data = [{"full_name": row[5], "name": row[5]}]
        self.restore(self._ASSET_BUSINESS_FIELD, data, result)

    def _parse_domain(self, row, result):
        self.restore(self._DOMAIN_FIELD, row[4].split("\n"), result)

    @classmethod
    def _entry_parse_core(cls, row, target_index, index, check_len):
        data = []
        for ip in row[target_index].split("\n"):
            if not ip:
                continue
            ip_info = ip.split(":")
            if len(ip_info) >= check_len:
                data.append(ip_info[index])
        return data

    @classmethod
    def get_asset_type(cls, record):
        return "network_mapping"

    @staticmethod
    def get_category():
        return "network"

    @staticmethod
    def get_model_name():
        return "network_mapping"

    @staticmethod
    def get_data_type():
        return "network_mapping"

    @staticmethod
    def get_parser_name():
        return "【资产映射】互联网离线资产数据导入"


CLASS = NetworkMappingImport
