from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class BusinessAliasImporter(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0]

    _BUSINESS_NAME_FIELD = "business.name"
    _BUSINESS_FULL_NAME_FIELD = "business.full_name"
    _BUSINESS_ALIAS_FIELD = "business.alias"
    _BUSINESS_ALIAS_CODE_FIELD = "business.alias_code"

    def _parse_name(self, row, result):
        self.restore(self._BUSINESS_NAME_FIELD, row[0], result)

    def _parse_alias(self, row, result):
        self.restore(self._BUSINESS_ALIAS_FIELD, row[1], result)

    def _parse_alias_code(self, row, result):
        self.restore(self._BUSINESS_ALIAS_CODE_FIELD, row[2], result)

    def _parse_full_name(self, row, result):
        self.restore(self._BUSINESS_FULL_NAME_FIELD, row[0], result)

    @staticmethod
    def get_parser_name():
        return "业务系统上报别名表格导入"

    @staticmethod
    def get_category():
        return "business"

    @staticmethod
    def get_model_name():
        return "business"

    @staticmethod
    def get_data_type():
        return "business"

    @classmethod
    def get_asset_type(cls, record):
        return "business"


CLASS = BusinessAliasImporter
