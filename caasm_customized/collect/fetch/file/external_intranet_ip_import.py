#   外连局域网IP网段导入
from IPy import IP

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class ExternalIntranetIPImporter(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0]
    _COMMENT_FIELD_NAME = "asset_base.comment"
    _ENV_FILED_NAME = "asset_base.env"
    _SUBNET_FIELD_NAME = "intranet_ip.subnet"
    _IP_FIELD_NAME = "intranet_ip.ip"
    _IPv4_FIELD_NAME = "intranet_ip.ipv4"
    _IPv6_FIELD_NAME = "intranet_ip.ipv6"
    _DEPARTMENTS_FIELD_NAME = "asset_base.departments"
    _OWNERS_FIELD_NAME = "asset_base.owners"
    _NAME_FIELD_NAME = "asset_base.name"

    def _parse_comment(self, row, result):
        comment = row[0]
        if comment:
            self.restore(self._COMMENT_FIELD_NAME, comment, result)

    def _parse_env(self, row, result):
        env = row[1]
        if env:
            self.restore(self._ENV_FILED_NAME, env, result)

    def _parse_subnet(self, row, result):
        subnet = row[2]
        if subnet:
            self.restore(self._SUBNET_FIELD_NAME, subnet, result)

    def _parse_ip(self, row, result):
        ip = row[3]
        if ip:
            try:
                py_ip = IP(ip)
            except ValueError:
                return
            self.restore(self._IP_FIELD_NAME, ip, result)
            if py_ip.version() == 4:
                self.restore(self._IPv4_FIELD_NAME, ip, result)
            else:
                self.restore(self._IPv6_FIELD_NAME, ip, result)
            self.restore(self._NAME_FIELD_NAME, ip, result)

    def _parse_department(self, row, result):
        department_name = row[4]
        if department_name:
            self.restore(
                self._DEPARTMENTS_FIELD_NAME,
                [{"name": department_name, "full_name": department_name, "type": 3}],
                result,
            )

    def _parse_owner(self, row, result):
        owner_name = row[5]
        if owner_name:
            self.restore(
                self._OWNERS_FIELD_NAME,
                [
                    {
                        "nickname": owner_name,
                    }
                ],
                result,
            )

    @staticmethod
    def get_parser_name():
        return "外联IP清单"

    @staticmethod
    def get_category():
        return "asset"

    @staticmethod
    def get_model_name():
        return "intranet_ip"

    @staticmethod
    def get_data_type():
        return "intranet_ip"

    @classmethod
    def get_asset_type(cls, record):
        return "intranet_ip"

    def clean_result(self, result):
        #   空IP地址的不是局域网IP地址
        data = {}

        for record in result:
            ip = self.extract(record, self._IP_FIELD_NAME)
            if ip:
                data[ip] = record

        return list(data.values())


CLASS = ExternalIntranetIPImporter
