from collections import defaultdict

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class HostOfflineDataExporter(XlsxFileParserV1):
    INVALID_ROW_INDEXES = [0]
    VALID_SHEET_INDEXES = [4, 5, 6, 7]

    _BUSINESS_NAME_FIELD = "asset_base.businesses"
    _DATA_CENTER_FIELD = "asset_base.data_center"
    _OS_DISTRIBUTION_FIELD = "computer.os.distribution"
    _DEVICE_MODEL_FIELD = "computer.device.model"
    _IP_FIELD = "network.ips"
    _OWNER_FIELD = "asset_base.owners"

    def _parse_ip(self, row, result):
        ori_ip = row[4]
        if not ori_ip:
            return
        ips = ori_ip.split("\n")
        #   多IP地址不分解为多个主机，为小型机
        ips_list = []
        for ip in ips:
            if not ip:
                continue
            ips_list.append({"addr": ip})
        if ips_list:
            self.restore(self._IP_FIELD, ips_list, result)

    def _parse_business(self, row, result):
        name = row[0]
        module = row[1]
        sub_module = row[2]

        business = {"name": name, "full_name": name, "module": module, "sub_module": sub_module}
        self.restore(self._BUSINESS_NAME_FIELD, [business], result)

    def _parse_data_center(self, row, result):
        data_center = row[3]
        if not data_center:
            return
        self.restore(self._DATA_CENTER_FIELD, data_center, result)

    def _parse_owner(self, row, result):
        owner_name = row[5]
        if not owner_name:
            return
        owner_name = owner_name.strip()
        owners = [{"nickname": owner_name, "username": owner_name}]
        self.restore(self._OWNER_FIELD, owners, result)

    def _parse_device(self, row, result):
        info = row[9]
        if not info:
            return
        self.restore(self._DEVICE_MODEL_FIELD, info, result)

    def _parse_os(self, row, result):
        info = row[7]
        if not info:
            return
        self.restore(self._OS_DISTRIBUTION_FIELD, info, result)

    def clean_result(self, result):
        hosts_by_ip = defaultdict(list)

        for record in result:
            ips = self.extract(record, self._IP_FIELD)
            if not ips:
                continue
            if len(ips) == 1:
                ip_key = self.extract(ips[0], "addr")
            else:
                ip_list = []
                for ip in ips:
                    ip_list.append(self.extract(ip, "addr"))
                ip_key = ";".join(ip_list)
            hosts_by_ip[ip_key].append(record)

        cleand_hosts = []
        for _, records in hosts_by_ip.items():
            if len(records) == 1:
                cleand_hosts.append(records[0])
            else:
                businesses = []
                for record in records:
                    businesses.extend(self.extract(record, self._BUSINESS_NAME_FIELD))
                cleand_host = records[0]
                self.restore(self._BUSINESS_NAME_FIELD, businesses, cleand_host)
                cleand_hosts.append(cleand_host)

        return cleand_hosts

    @staticmethod
    def get_parser_name():
        return "主机离线数据导入"

    @staticmethod
    def get_category():
        return "asset"

    @staticmethod
    def get_model_name():
        return "computer"

    @staticmethod
    def get_data_type():
        return "host"

    @classmethod
    def get_asset_type(cls, record):
        return "host"


CLASS = HostOfflineDataExporter
