import copy
import itertools
import re

from caasm_file.parsers.xlsx_parse import XlsxFileParserV1


class InternetEntryImport(XlsxFileParserV1):
    _DOMAIN_FIELD = "internet_entry.domain"
    _INTERNET_IPS_FIELD = "internet_entry.internet_ips"
    _INTERNET_PORTS_FIELD = "internet_entry.ports"
    _SYS_NAME_FIELD = "internet_entry.sys_name"

    _INTRANET_IP_FIELD = "internet_entry.intranet_ip"
    _INTRANET_PORTS_FIELD = "internet_entry.intranet_ports"

    _ASSET_OWNER_FIELD = "asset_base.owners"
    _ASSET_BUSINESS_FIELD = "asset_base.businesses"

    def clean(self, data):
        result = {}

        dest_mappers = self.extract(data, self._INTRANET_IP_FIELD) or [{}]
        domains = self.extract(data, self._DOMAIN_FIELD) or [None]

        for domain, dest_info in itertools.product(domains, dest_mappers):

            for dest_ip, dest_ports in dest_info.items():
                result = copy.deepcopy(data)
                self.restore(self._INTRANET_IP_FIELD, dest_ip, result)
                self.restore(self._INTRANET_PORTS_FIELD, dest_ports, result)
                self.restore(self._DOMAIN_FIELD, domain, result)

            if not dest_info:
                result = copy.deepcopy(data)
                self.restore(self._DOMAIN_FIELD, domain, result)

        return result

    def _parse_internet_ips(self, row, result):
        ips = row[0]
        if not ips:
            return
        self.restore(self._INTERNET_IPS_FIELD, [row[0]], result)

    def _parse_internet_ports(self, row, result):
        port = self._to_int(row[1])
        if port is None:
            return
        self.restore(self._INTERNET_PORTS_FIELD, [port], result)

    def _parse_destinations(self, row, result):
        info = self._dest_parse_core(row[8]) or self._dest_parse_core(row[9])
        self.restore(self._INTRANET_IP_FIELD, [info], result)

    @classmethod
    def _dest_parse_core(cls, info):
        ip_mapper = {}
        for dest_info in info.split("\n"):
            if not dest_info:
                continue
            dest_ip = dest_port = None
            dest_more = dest_info.split(":")
            dest_more_len = len(dest_more)
            if dest_more_len >= 1:
                dest_ip = dest_more[0]
            if dest_more_len >= 2:
                dest_port = dest_more[1]
            if dest_ip not in ip_mapper:
                ip_mapper[dest_ip] = []
            if dest_port:
                ip_mapper[dest_ip].append(dest_port)

        return ip_mapper

    def _parse_sys_name(self, row, result):
        self.restore(self._SYS_NAME_FIELD, row[5], result)

    def _parse_owner(self, row, result):
        data = []
        for owner in row[10].split("/"):
            data.append(
                {
                    "username": owner,
                    "nickname": owner,
                }
            )
        self.restore(self._ASSET_OWNER_FIELD, data, result)

    def _parse_business(self, row, result):
        if not row[5]:
            return
        data = [{"full_name": row[5], "name": row[5]}]
        self.restore(self._ASSET_BUSINESS_FIELD, data, result)

    def _parse_domain(self, row, result):
        self.restore(self._DOMAIN_FIELD, row[4].split("\n"), result)

    @classmethod
    def _to_int(cls, data):
        if data is None or data == "":
            return data
        if isinstance(data, float):
            return int(data)

        if isinstance(data, str):
            info = re.search(r"\d+", data)
            if info:
                return int(info[0])
        return None

    @classmethod
    def get_asset_type(cls, record):
        return "internet_entry"

    @staticmethod
    def get_category():
        return "network"

    @staticmethod
    def get_model_name():
        return "internet_entry"

    @staticmethod
    def get_data_type():
        return "internet_entry"

    @staticmethod
    def get_parser_name():
        return "【互联网入口】互联网离线资产数据导入"


CLASS = InternetEntryImport
