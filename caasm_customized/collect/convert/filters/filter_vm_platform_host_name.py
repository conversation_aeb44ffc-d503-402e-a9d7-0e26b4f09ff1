from caasm_adapter.convert.filters.base import ConvertBaseFilter


class FilterVMPlatformHost(ConvertBaseFilter):
    HOST_NAME_FIELD = "vm_NAME"
    IP_FIELD = "ip"
    HOST_NAME_PREFIX = "shtemplate"

    def my_filter(self, record):
        host_name = self.extract(record, self.HOST_NAME_FIELD)
        ip = self.extract(record, self.IP_FIELD)

        if not host_name:
            return self.allow()

        if ip and isinstance(host_name, str) and host_name.lower().startswith(self.HOST_NAME_PREFIX):
            return self.deny()
        return self.allow()
