from caasm_adapter.convert.filters.base import ConvertBaseFilter


class FilterQingtengOfflineHost(ConvertBaseFilter):
    AGENT_STATUS = "agentStatus"
    OFFLINE_VALUES = (1, 2, 3)

    def my_filter(self, record):
        agent_status = self.extract(record, self.AGENT_STATUS)

        if agent_status is None:
            return self.allow()

        if agent_status in self.OFFLINE_VALUES:
            return self.deny()

        return self.allow()
