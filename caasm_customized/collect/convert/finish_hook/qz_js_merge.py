from collections import defaultdict

from IPy import IP

from caasm_persistence.handler.runtime import mongo_handler
from caasm_tool.util import extract

_IP_FIELD = "network.ips"
_LAST_SEEN_FIELD = "base.last_seen"


def main(table):
    records = _find_record(table)
    record_mapper = _group_record(records)
    _rebuild_table(table, record_mapper)


def _rebuild_table(table, record_mapper):
    mongo_handler.drop(table)

    data = record_mapper.pop(None, [])
    for ip, records in record_mapper.items():
        record = max(records, key=lambda x: extract(x, _LAST_SEEN_FIELD) or 0) if len(records) != 1 else records[0]
        data.append(record)

    mongo_handler.save_stream(data, table=table)


def _group_record(records):
    result = defaultdict(list)
    for record in records:
        record.pop("_id")
        ips = extract(record, _IP_FIELD) or [None]
        for ip_record in ips:
            try:
                ip = ip_record.get("addr")
                ip_obj = IP(ip)
            except Exception as e:
                result[None].append(record)
            else:
                if ip_obj.iptype() != "LOOPBACK":
                    result[ip].append(record)
                else:
                    result[None].append(record)
    return result


def _find_record(table, size=200):
    result = []
    offset = 0
    while True:
        records = list(mongo_handler.find_direct(None, offset=offset, limit=size, table=table))
        if not records:
            break
        result.extend(records)
        offset += size
    return result
